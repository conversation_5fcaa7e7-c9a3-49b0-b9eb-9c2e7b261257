package promotion;

import com.suncent.smc.SuncentSmcApplication;
import com.suncent.smc.provider.biz.promotion.AmazonVcPromotionBiz;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 亚马逊VC促销活动业务测试类
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@SpringBootTest(classes = {SuncentSmcApplication.class})
@RunWith(SpringRunner.class)
public class AmazonVcPromotionBizTest {

    @Autowired
    private AmazonVcPromotionBiz amazonVcPromotionBiz;

    /**
     * 测试构建促销活动JSON
     */
    @Test
    public void testBuildPromotionJson() {
        // 使用一个测试的promotion_id
        String testPromotionId = "7a393faa-9f7f-401a-8b80-32d9ac1d6fe8";

        try {
            // 测试构建JSON
            String jsonResult = amazonVcPromotionBiz.buildAddBDPromotionJson(testPromotionId);

            log.info("构建的促销活动JSON:");
            log.info(jsonResult);

        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }


}
