package com.suncent.smc.provider.biz.promotion.dto;

import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsin;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * BD活动完整数据DTO
 * 用于封装整个BD活动的所有相关数据
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
public class BdActivityDTO {

    /**
     * BD活动基本信息
     */
    private BdActivityInfo activityInfo;

    /**
     * BD活动ASIN列表
     */
    private List<BdAsinInfo> asinList;

    /**
     * 操作信息
     */
    private OperationInfo operationInfo;

    /**
     * BD活动基本信息
     */
    @Data
    public static class BdActivityInfo {
        /** 主键ID */
        private Long id;

        /** 刊登类型，5：VCDF，6：VCPO */
        private Integer publishType;

        /** 站点 */
        private String site;

        /** 促销ID，亚马逊后台的ID */
        private String promotionId;

        /** 促销活动名称 */
        private String promotionName;

        /** 资金协议ID */
        private String fundingAgreementId;

        /** 状态,DRAFT,NEEDS_YOUR_ATTENTION,CANCELED,APPROVED */
        private String status;

        /** 1:BD,2:LD */
        private Integer dealType;

        /** 1:自定义日期，2：会员日，3：黑五 */
        private Integer eventType;

        /** 开始时间UTC */
        private String startDateUtc;

        /** 结束时间UTC */
        private String endDateUtc;

        /** 亚马逊后台创建时间 */
        private String createdDateTime;

        /** 亚马逊后台最后修改时间 */
        private String lastUpdateDateTime;

        /** SMC标识 */
        private Integer smcFlag;

        /** ASIN数量 */
        private Integer asinCount;

        /** 创建时间 */
        private Date createTime;

        /** 更新时间 */
        private Date updateTime;

        /** 创建人 */
        private String createBy;

        /** 更新人 */
        private String updateBy;
    }

    /**
     * BD活动ASIN信息
     */
    @Data
    public static class BdAsinInfo {
        /** 主键ID */
        private Long id;

        /** 关联BD记录ID */
        private Long refBestDealId;

        /** 链接头部ID，用于标识具体的商品链接 */
        private Long headId;

        /** 促销ID，亚马逊后台的ID */
        private String promotionId;

        /** ASIN */
        private String platformGoodsId;

        /** 平台商品编码 */
        private String platformGoodsCode;

        /** PDM商品编码 */
        private String pdmGoodsCode;

        /** cost price/your price */
        private BigDecimal standardPrice;

        /** 引用价，实际参与计算折扣的价格 */
        private BigDecimal referencePrice;

        /** 预期需求量 */
        private Integer expectedDemand;

        /** 承诺数量 */
        private Integer committedUnits;

        /** 最低折扣，百分比 */
        private Integer lowestDiscount;

        /** 实际折扣，百分比 */
        private Integer actualDiscount;

        /** 促销价 */
        private BigDecimal dealPrice;

        /** 单位折扣金额 */
        private BigDecimal perUnitFunding;

        /** 创建时间 */
        private Date createTime;

        /** 更新时间 */
        private Date updateTime;

        /** 创建人 */
        private String createBy;

        /** 更新人 */
        private String updateBy;

        /** 操作标识：ADD-新增, UPDATE-修改, DELETE-删除, NONE-无变化 */
        private String operationFlag;

        /**
         * 转换为AmBestDealAsin
         *
         * @return AmBestDealAsin
         */
        public AmBestDealAsin toAmBestDealAsin() {
            AmBestDealAsin asin = new AmBestDealAsin();
            asin.setId(this.id);
            asin.setRefBestDealId(this.refBestDealId);
            asin.setHeadId(this.headId);
            asin.setPromotionId(this.promotionId);
            asin.setPlatformGoodsId(this.platformGoodsId);
            asin.setPlatformGoodsCode(this.platformGoodsCode);
            asin.setPdmGoodsCode(this.pdmGoodsCode);
            asin.setStandardPrice(this.standardPrice);
            asin.setReferencePrice(this.referencePrice);
            asin.setExpectedDemand(this.expectedDemand);
            asin.setCommittedUnits(this.committedUnits);
            asin.setLowestDiscount(this.lowestDiscount);
            asin.setActualDiscount(this.actualDiscount);
            asin.setDealPrice(this.dealPrice);
            asin.setPerUnitFunding(this.perUnitFunding);
            asin.setCreateTime(this.createTime);
            asin.setUpdateTime(this.updateTime);
            asin.setCreateBy(this.createBy);
            asin.setUpdateBy(this.updateBy);
            return asin;
        }
    }

    /**
     * 操作信息
     */
    @Data
    public static class OperationInfo {
        /** 操作类型：ADD-新增活动, UPDATE-更新活动 */
        private String operationType;

        /** 操作人 */
        private String operatorId;

        /** 操作人姓名 */
        private String operatorName;

        /** 操作时间 */
        private Date operationTime;

        /** 操作备注 */
        private String remark;

        /** 是否有数据变更 */
        private Boolean hasChanges;

        /** 变更统计 */
        private ChangeStatistics changeStatistics;
    }

    /**
     * 变更统计信息
     */
    @Data
    public static class ChangeStatistics {
        /** 新增ASIN数量 */
        private Integer addedAsinCount = 0;

        /** 修改ASIN数量 */
        private Integer modifiedAsinCount = 0;

        /** 删除ASIN数量 */
        private Integer removedAsinCount = 0;

        /** 总ASIN数量 */
        private Integer totalAsinCount = 0;

        /** 活动信息是否变更 */
        private Boolean activityInfoChanged = false;
    }

    /**
     * 构造函数
     */
    public BdActivityDTO() {
        this.asinList = new ArrayList<>();
        this.operationInfo = new OperationInfo();
        this.operationInfo.setChangeStatistics(new ChangeStatistics());
    }

    /**
     * 从AmBestDealRecord转换为BdActivityDTO
     *
     * @param record BD记录
     * @return BdActivityDTO
     */
    public static BdActivityDTO fromAmBestDealRecord(AmBestDealRecord record) {
        if (record == null) {
            return null;
        }

        BdActivityDTO dto = new BdActivityDTO();

        // 转换活动基本信息
        BdActivityInfo activityInfo = new BdActivityInfo();
        activityInfo.setId(record.getId());
        activityInfo.setPublishType(record.getPublishType());
        activityInfo.setSite(record.getSite());
        activityInfo.setPromotionId(record.getPromotionId());
        activityInfo.setPromotionName(record.getPromotionName());
        activityInfo.setFundingAgreementId(record.getFundingAgreementId());
        activityInfo.setStatus(record.getStatus());
        activityInfo.setDealType(record.getDealType());
        activityInfo.setEventType(record.getEventType());
        activityInfo.setStartDateUtc(record.getStartDateUtc());
        activityInfo.setEndDateUtc(record.getEndDateUtc());
        activityInfo.setCreatedDateTime(record.getCreatedDateTime());
        activityInfo.setLastUpdateDateTime(record.getLastUpdateDateTime());
        activityInfo.setSmcFlag(record.getSmcFlag());
        activityInfo.setAsinCount(record.getAsinCount());
        activityInfo.setCreateTime(record.getCreateTime());
        activityInfo.setUpdateTime(record.getUpdateTime());
        activityInfo.setCreateBy(record.getCreateBy());
        activityInfo.setUpdateBy(record.getUpdateBy());

        dto.setActivityInfo(activityInfo);

        // 转换ASIN列表
        if (record.getAsinList() != null) {
            List<BdAsinInfo> asinInfoList = new ArrayList<>();
            for (AmBestDealAsin asin : record.getAsinList()) {
                BdAsinInfo asinInfo = fromAmBestDealAsin(asin);
                if (asinInfo != null) {
                    asinInfoList.add(asinInfo);
                }
            }
            dto.setAsinList(asinInfoList);
        }

        return dto;
    }

    /**
     * 从AmBestDealAsin转换为BdAsinInfo
     *
     * @param asin ASIN记录
     * @return BdAsinInfo
     */
    public static BdAsinInfo fromAmBestDealAsin(AmBestDealAsin asin) {
        if (asin == null) {
            return null;
        }

        BdAsinInfo asinInfo = new BdAsinInfo();
        asinInfo.setId(asin.getId());
        asinInfo.setRefBestDealId(asin.getRefBestDealId());
        asinInfo.setHeadId(asin.getHeadId());
        asinInfo.setPromotionId(asin.getPromotionId());
        asinInfo.setPlatformGoodsId(asin.getPlatformGoodsId());
        asinInfo.setPlatformGoodsCode(asin.getPlatformGoodsCode());
        asinInfo.setPdmGoodsCode(asin.getPdmGoodsCode());
        asinInfo.setStandardPrice(asin.getStandardPrice());
        asinInfo.setReferencePrice(asin.getReferencePrice());
        asinInfo.setExpectedDemand(asin.getExpectedDemand());
        asinInfo.setCommittedUnits(asin.getCommittedUnits());
        asinInfo.setLowestDiscount(asin.getLowestDiscount());
        asinInfo.setActualDiscount(asin.getActualDiscount());
        asinInfo.setDealPrice(asin.getDealPrice());
        asinInfo.setPerUnitFunding(asin.getPerUnitFunding());
        asinInfo.setCreateTime(asin.getCreateTime());
        asinInfo.setUpdateTime(asin.getUpdateTime());
        asinInfo.setCreateBy(asin.getCreateBy());
        asinInfo.setUpdateBy(asin.getUpdateBy());
        asinInfo.setOperationFlag("NONE"); // 默认无变化

        return asinInfo;
    }

    /**
     * 转换为AmBestDealRecord
     *
     * @return AmBestDealRecord
     */
    public AmBestDealRecord toAmBestDealRecord() {
        if (this.activityInfo == null) {
            return null;
        }

        AmBestDealRecord record = new AmBestDealRecord();
        BdActivityInfo info = this.activityInfo;

        record.setId(info.getId());
        record.setPublishType(info.getPublishType());
        record.setSite(info.getSite());
        record.setPromotionId(info.getPromotionId());
        record.setPromotionName(info.getPromotionName());
        record.setFundingAgreementId(info.getFundingAgreementId());
        record.setStatus(info.getStatus());
        record.setDealType(info.getDealType());
        record.setEventType(info.getEventType());
        record.setStartDateUtc(info.getStartDateUtc());
        record.setEndDateUtc(info.getEndDateUtc());
        record.setCreatedDateTime(info.getCreatedDateTime());
        record.setLastUpdateDateTime(info.getLastUpdateDateTime());
        record.setSmcFlag(info.getSmcFlag());
        record.setAsinCount(info.getAsinCount());
        record.setCreateTime(info.getCreateTime());
        record.setUpdateTime(info.getUpdateTime());
        record.setCreateBy(info.getCreateBy());
        record.setUpdateBy(info.getUpdateBy());

        // 转换ASIN列表
        if (this.asinList != null && !this.asinList.isEmpty()) {
            List<AmBestDealAsin> asinEntityList = new ArrayList<>();
            for (BdAsinInfo asinInfo : this.asinList) {
                AmBestDealAsin asinEntity = asinInfo.toAmBestDealAsin();
                if (asinEntity != null) {
                    asinEntityList.add(asinEntity);
                }
            }
            record.setAsinList(asinEntityList);
        }

        return record;
    }

    /**
     * 添加ASIN信息
     *
     * @param asinInfo ASIN信息
     */
    public void addAsinInfo(BdAsinInfo asinInfo) {
        if (this.asinList == null) {
            this.asinList = new ArrayList<>();
        }
        this.asinList.add(asinInfo);

        // 更新统计信息
        if (this.operationInfo != null && this.operationInfo.getChangeStatistics() != null) {
            this.operationInfo.getChangeStatistics().setTotalAsinCount(this.asinList.size());
        }
    }

    /**
     * 移除ASIN信息
     *
     * @param platformGoodsId ASIN
     */
    public void removeAsinInfo(String platformGoodsId) {
        if (this.asinList != null) {
            this.asinList.removeIf(asin -> platformGoodsId.equals(asin.getPlatformGoodsId()));

            // 更新统计信息
            if (this.operationInfo != null && this.operationInfo.getChangeStatistics() != null) {
                this.operationInfo.getChangeStatistics().setTotalAsinCount(this.asinList.size());
            }
        }
    }

    /**
     * 根据ASIN查找ASIN信息
     *
     * @param platformGoodsId ASIN
     * @return BdAsinInfo
     */
    public BdAsinInfo findAsinInfo(String platformGoodsId) {
        if (this.asinList == null || platformGoodsId == null) {
            return null;
        }

        return this.asinList.stream()
                .filter(asin -> platformGoodsId.equals(asin.getPlatformGoodsId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为新增活动
     *
     * @return 是否为新增活动
     */
    public boolean isNewActivity() {
        return this.activityInfo == null ||
               this.activityInfo.getId() == null ||
               this.activityInfo.getPromotionId() == null ||
               this.activityInfo.getPromotionId().trim().isEmpty();
    }

    /**
     * 设置操作信息
     *
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param remark 备注
     */
    public void setOperationInfo(String operatorId, String operatorName, String remark) {
        if (this.operationInfo == null) {
            this.operationInfo = new OperationInfo();
            this.operationInfo.setChangeStatistics(new ChangeStatistics());
        }

        this.operationInfo.setOperatorId(operatorId);
        this.operationInfo.setOperatorName(operatorName);
        this.operationInfo.setRemark(remark);
        this.operationInfo.setOperationTime(new Date());
        this.operationInfo.setOperationType(isNewActivity() ? "ADD" : "UPDATE");
    }

    /**
     * 更新变更统计
     *
     * @param addedCount 新增数量
     * @param modifiedCount 修改数量
     * @param removedCount 删除数量
     * @param activityInfoChanged 活动信息是否变更
     */
    public void updateChangeStatistics(int addedCount, int modifiedCount, int removedCount, boolean activityInfoChanged) {
        if (this.operationInfo == null) {
            this.operationInfo = new OperationInfo();
            this.operationInfo.setChangeStatistics(new ChangeStatistics());
        }

        ChangeStatistics stats = this.operationInfo.getChangeStatistics();
        stats.setAddedAsinCount(addedCount);
        stats.setModifiedAsinCount(modifiedCount);
        stats.setRemovedAsinCount(removedCount);
        stats.setTotalAsinCount(this.asinList != null ? this.asinList.size() : 0);
        stats.setActivityInfoChanged(activityInfoChanged);

        // 判断是否有变更
        boolean hasChanges = addedCount > 0 || modifiedCount > 0 || removedCount > 0 || activityInfoChanged;
        this.operationInfo.setHasChanges(hasChanges);
    }
}
