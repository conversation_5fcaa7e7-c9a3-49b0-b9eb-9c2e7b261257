package com.suncent.smc.provider.biz.promotion;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsin;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask;
import com.suncent.smc.persistence.promotion.service.IAmBestDealAsinService;
import com.suncent.smc.persistence.promotion.service.IAmBestDealRecordService;
import com.suncent.smc.persistence.promotion.service.IAmPromotionRpaTaskService;
import com.suncent.smc.provider.biz.promotion.dto.BdActivityDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 亚马逊VC促销活动业务处理类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class AmazonVcPromotionBiz {

    @Autowired
    private IAmBestDealRecordService amBestDealRecordService;

    @Autowired
    private IAmBestDealAsinService amBestDealAsinService;

    @Autowired
    private IAmPromotionRpaTaskService amPromotionRpaTaskService;

    /**
     * 根据promotion_id构建亚马逊VC促销活动JSON（简化格式）
     * 新增活动使用
     *
     * @param promotionId 促销ID
     * @return 促销活动JSON字符串
     */
    public String buildAddBDPromotionJson(String promotionId) {
        if (StrUtil.isBlank(promotionId)) {
            throw new BusinessException("促销ID不能为空");
        }

        // 1. 查询促销记录
        AmBestDealRecord promotionRecord = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
        if (promotionRecord == null) {
            throw new BusinessException("未找到促销记录，促销ID: " + promotionId);
        }

        // 2. 查询关联的ASIN列表
        List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(promotionRecord.getId());
        if (CollUtil.isEmpty(asinList)) {
            throw new BusinessException("未找到关联的ASIN记录，促销ID: " + promotionId);
        }

        // 3. 构建简化的促销活动JSON
        Map<String, Object> result = buildSimplifiedPromotionData(promotionRecord, asinList);

        // 4. 转换为JSON字符串
        String jsonResult = JSONUtil.toJsonPrettyStr(result);

        log.info("成功构建促销活动JSON，促销ID: {}, ASIN数量: {}", promotionId, asinList.size());

        return jsonResult;
    }

    /**
     * 保存修改BD活动
     * 比较新旧数据，生成对应的新增或更新JSON，并写入RPA任务表
     *
     * @param newActivityData 新的BD活动数据
     * @param oldActivityData 旧的BD活动数据（可为null，表示新增）
     * @return 操作结果
     */
    public String saveModifyBdActivity(BdActivityDTO newActivityData, BdActivityDTO oldActivityData) {
        if (newActivityData == null || newActivityData.getActivityInfo() == null) {
            throw new BusinessException("新活动数据不能为空");
        }

        try {
            // 设置操作信息
            if (newActivityData.getOperationInfo() == null) {
                throw new BusinessException("操作信息不能为空");
            }

            // 判断是新增还是更新操作
            boolean isNewPromotion = newActivityData.isNewActivity() ||
                                   (oldActivityData == null || oldActivityData.isNewActivity());

            String operationType;
            String executeJson;
            String promotionId = newActivityData.getActivityInfo().getPromotionId();
            String operatorId = newActivityData.getOperationInfo().getOperatorId();

            if (isNewPromotion) {
                // 新增操作
                operationType = AmPromotionRpaTask.OperationType.ADD.getCode();
                executeJson = this.buildAddBDPromotionJson(promotionId);
                log.info("创建新增BD活动任务，促销ID: {}, 操作人: {}", promotionId, operatorId);

                // 更新统计信息
                int totalAsins = newActivityData.getAsinList() != null ? newActivityData.getAsinList().size() : 0;
                newActivityData.updateChangeStatistics(totalAsins, 0, 0, true);
            } else {
                // 更新操作 - 比较新旧数据
                BdActivityComparisonResult comparisonResult = compareBdActivityData(newActivityData, oldActivityData);

                if (comparisonResult.hasChanges()) {
                    operationType = AmPromotionRpaTask.OperationType.UPDATE.getCode();
                    executeJson = buildUpdatePromotionJson(promotionId);
                    log.info("创建更新BD活动任务，促销ID: {}, 操作人: {}, 变更统计: 新增{}个, 修改{}个, 删除{}个",
                            promotionId, operatorId,
                            comparisonResult.getAddedAsins().size(),
                            comparisonResult.getModifiedAsins().size(),
                            comparisonResult.getRemovedAsins().size());

                    // 更新统计信息
                    newActivityData.updateChangeStatistics(
                            comparisonResult.getAddedAsins().size(),
                            comparisonResult.getModifiedAsins().size(),
                            comparisonResult.getRemovedAsins().size(),
                            comparisonResult.isActivityInfoChanged()
                    );
                } else {
                    log.info("BD活动数据无变更，无需创建RPA任务，促销ID: {}, 操作人: {}", promotionId, operatorId);
                    newActivityData.updateChangeStatistics(0, 0, 0, false);
                    return "数据无变更，无需创建任务";
                }
            }

            // 创建RPA任务
            int result = amPromotionRpaTaskService.createPromotionRpaTask(
                    newActivityData.getActivityInfo().getId(),
                    operationType,
                    promotionId,
                    newActivityData.getActivityInfo().getPublishType(),
                    newActivityData.getActivityInfo().getSite(),
                    1, // BD活动类型
                    executeJson,
                    operatorId
            );

            if (result > 0) {
                String message = isNewPromotion ? "新增BD活动任务创建成功" : "更新BD活动任务创建成功";
                log.info("{}, 促销ID: {}, 操作人: {}", message, promotionId, operatorId);
                return message;
            } else {
                throw new BusinessException("RPA任务创建失败");
            }

        } catch (Exception e) {
            String promotionId = newActivityData.getActivityInfo() != null ?
                               newActivityData.getActivityInfo().getPromotionId() : "未知";
            log.error("保存修改BD活动失败，促销ID: {}", promotionId, e);
            throw new BusinessException("保存修改BD活动失败: " + e.getMessage());
        }
    }

    /**
     * 根据promotion_id构建亚马逊VC促销活动更新JSON
     * 更新活动使用
     *
     * @param promotionId 促销ID
     * @return 促销活动更新JSON字符串
     */
    public String buildUpdatePromotionJson(String promotionId) {
        if (StrUtil.isBlank(promotionId)) {
            throw new BusinessException("促销ID不能为空");
        }

        // 1. 查询促销记录
        AmBestDealRecord promotionRecord = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
        if (promotionRecord == null) {
            throw new BusinessException("未找到促销记录，促销ID: " + promotionId);
        }

        // 2. 查询关联的ASIN列表
        List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(promotionRecord.getId());
        if (CollUtil.isEmpty(asinList)) {
            throw new BusinessException("未找到关联的ASIN记录，促销ID: " + promotionId);
        }

        // 3. 构建更新促销活动JSON
        Map<String, Object> result = buildUpdatePromotionData(promotionRecord, asinList);

        // 4. 转换为JSON字符串
        String jsonResult = JSONUtil.toJsonPrettyStr(result);

        log.info("成功构建更新促销活动JSON，促销ID: {}, ASIN数量: {}", promotionId, asinList.size());

        return jsonResult;
    }


    /**
     * 构建简化的促销活动数据（新格式）
     *
     * @param record   促销记录
     * @param asinList ASIN列表
     * @return 简化的促销活动数据
     */
    private Map<String, Object> buildSimplifiedPromotionData(AmBestDealRecord record, List<AmBestDealAsin> asinList) {
        Map<String, Object> result = new HashMap<>();

        // 构建产品列表
        List<Map<String, Object>> products = new ArrayList<>();
        for (AmBestDealAsin asin : asinList) {
            Map<String, Object> product = new HashMap<>();
            product.put("asin", asin.getPlatformGoodsId());
            product.put("sku", asin.getPlatformGoodsId());

            // 促销数量
            Map<String, Object> promotionQuantity = new HashMap<>();
            promotionQuantity.put("units", asin.getCommittedUnits() != null ? asin.getCommittedUnits() : 0);
            product.put("promotionQuantity", promotionQuantity);

            // 单位资金
            Map<String, Object> perUnitFunding = new HashMap<>();
            Map<String, Object> fundingValue = new HashMap<>();
            fundingValue.put("amount", asin.getPerUnitFunding() != null ? asin.getPerUnitFunding() : BigDecimal.ZERO);
            fundingValue.put("currencyCode", "USD");
            perUnitFunding.put("value", fundingValue);
            product.put("perUnitFunding", perUnitFunding);

            // 促销价格
            Map<String, Object> promotionPrice = new HashMap<>();
            Map<String, Object> priceValue = new HashMap<>();
            priceValue.put("amount", asin.getDealPrice() != null ? asin.getDealPrice() : BigDecimal.ZERO);
            priceValue.put("currencyCode", "USD");
            promotionPrice.put("value", priceValue);
            product.put("promotionPrice", promotionPrice);

            products.add(product);
        }
        result.put("products", products);

        // 构建促销信息
        Map<String, Object> promotion = new HashMap<>();
        promotion.put("internalDescription", buildInternalDescription(record));

        // 特色ASIN（使用第一个ASIN）
        if (!asinList.isEmpty()) {
            promotion.put("featuredAsin", asinList.get(0).getPlatformGoodsId());
        }

        promotion.put("marketplaceId", getMarketplaceId(record.getSite()));
        promotion.put("offeringName", "BEST_DEAL");

        // 时间安排
        Map<String, Object> schedule = new HashMap<>();
        schedule.put("startDate", convertToSimpleTimeFormat(record.getStartDateUtc()));
        schedule.put("endDate", convertToSimpleTimeFormat(record.getEndDateUtc()));
        promotion.put("schedule", schedule);

        // 所有者信息
        Map<String, Object> owner = new HashMap<>();
        owner.put("vendorCode", determineVendorCode(record.getPublishType(), record.getSite()));
        promotion.put("owner", owner);

        result.put("promotion", promotion);

        return result;
    }

    /**
     * 构建更新促销活动数据（更新格式）
     *
     * @param record   促销记录
     * @param asinList ASIN列表
     * @return 更新促销活动数据
     */
    private Map<String, Object> buildUpdatePromotionData(AmBestDealRecord record, List<AmBestDealAsin> asinList) {
        Map<String, Object> result = new HashMap<>();

        // 设置促销ID
        result.put("promotionId", record.getPromotionId());

        // 构建促销信息
        Map<String, Object> promotion = new HashMap<>();
        promotion.put("internalDescription", buildInternalDescription(record));

        // 特色ASIN（使用第一个ASIN）
        if (!asinList.isEmpty()) {
            promotion.put("featuredAsin", asinList.get(0).getPlatformGoodsId());
        }

        promotion.put("marketplaceId", getMarketplaceId(record.getSite()));
        promotion.put("offeringName", "BEST_DEAL");

        // 时间安排
        Map<String, Object> schedule = new HashMap<>();
        schedule.put("startDate", convertToSimpleTimeFormat(record.getStartDateUtc()));
        schedule.put("endDate", convertToSimpleTimeFormat(record.getEndDateUtc()));
        promotion.put("schedule", schedule);

        // 所有者信息
        Map<String, Object> owner = new HashMap<>();
        owner.put("vendorCode", determineVendorCode(record.getPublishType(), record.getSite()));
        promotion.put("owner", owner);

        result.put("promotion", promotion);

        // 构建更新的产品列表
        List<Map<String, Object>> updatedProducts = new ArrayList<>();
        for (AmBestDealAsin asin : asinList) {
            Map<String, Object> product = new HashMap<>();
            product.put("asin", asin.getPlatformGoodsId());
            product.put("sku", asin.getPlatformGoodsCode() != null ? asin.getPlatformGoodsCode() : asin.getPlatformGoodsId());

            // 促销数量
            Map<String, Object> promotionQuantity = new HashMap<>();
            promotionQuantity.put("units", asin.getCommittedUnits() != null ? asin.getCommittedUnits() : 0);
            product.put("promotionQuantity", promotionQuantity);

            // 单位资金
            Map<String, Object> perUnitFunding = new HashMap<>();
            Map<String, Object> fundingValue = new HashMap<>();
            fundingValue.put("amount", asin.getPerUnitFunding() != null ? asin.getPerUnitFunding() : BigDecimal.ZERO);
            fundingValue.put("currencyCode", "USD");
            perUnitFunding.put("value", fundingValue);
            product.put("perUnitFunding", perUnitFunding);

            // 促销价格
            Map<String, Object> promotionPrice = new HashMap<>();
            Map<String, Object> priceValue = new HashMap<>();
            priceValue.put("amount", asin.getDealPrice() != null ? asin.getDealPrice() : BigDecimal.ZERO);
            priceValue.put("currencyCode", "USD");
            promotionPrice.put("value", priceValue);
            product.put("promotionPrice", promotionPrice);

            updatedProducts.add(product);
        }

        result.put("updatedProducts", updatedProducts);

        // 移除的产品列表（暂时为空）
        result.put("removedProducts", new ArrayList<>());

        return result;
    }

    /**
     * 转换为简单时间格式 (ISO本地时间格式: yyyy-MM-ddTHH:mm:ss)
     */
    private String convertToSimpleTimeFormat(String utcTime) {
        if (StrUtil.isBlank(utcTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        try {
            // 处理带时区信息的时间格式，如: "2025-08-19T23:45:00-07:00[US/Pacific]"
            if (utcTime.contains("[") || utcTime.contains("+") || utcTime.contains("-07:00") || utcTime.contains("-06:00")) {
                // 提取时间部分，去掉时区信息
                String timePart = utcTime;
                if (timePart.contains("[")) {
                    timePart = timePart.substring(0, timePart.indexOf("["));
                }
                if (timePart.contains("+")) {
                    timePart = timePart.substring(0, timePart.indexOf("+"));
                }
                if (timePart.contains("-") && timePart.lastIndexOf("-") > 10) {
                    timePart = timePart.substring(0, timePart.lastIndexOf("-"));
                }
                return timePart;
            }

            // 如果已经是ISO格式，去掉Z后缀
            if (utcTime.contains("T")) {
                return utcTime.replace("Z", "");
            }

            // 如果是其他格式，直接返回
            return utcTime;

        } catch (Exception e) {
            log.error("时间格式转换失败: {}", utcTime, e);
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
    }


    /**
     * 构建内部描述
     */
    private String buildInternalDescription(AmBestDealRecord record) {
        String dateStr = "";
        if (StrUtil.isNotBlank(record.getStartDateUtc())) {
            try {
                // 解析日期并格式化为 "Aug 2, 2025" 格式
                String startDate = record.getStartDateUtc();
                // 如果包含时间部分，只取日期部分
                if (startDate.contains("T")) {
                    startDate = startDate.substring(0, startDate.indexOf("T"));
                }

                // 解析日期 (格式: yyyy-MM-dd)
                String[] dateParts = startDate.split("-");
                if (dateParts.length == 3) {
                    int year = Integer.parseInt(dateParts[0]);
                    int month = Integer.parseInt(dateParts[1]);
                    int day = Integer.parseInt(dateParts[2]);

                    // 月份名称数组
                    String[] monthNames = {"", "Jan", "Feb", "Mar", "Apr", "May", "Jun",
                            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};

                    dateStr = monthNames[month] + " " + day + ", " + year;
                } else {
                    dateStr = "Date";
                }
            } catch (Exception e) {
                dateStr = "Date";
            }
        }

        String vendorCode = determineVendorCode(record.getPublishType(), record.getSite());
        return dateStr + " " + vendorCode + " Best Deal";
    }

    /**
     * 获取市场ID
     */
    private String getMarketplaceId(String site) {
        if (site == null) return "ATVPDKIKX0DER";

        switch (site.toUpperCase()) {
            case "US":
                return "ATVPDKIKX0DER";
            case "UK":
                return "A1F83G8C2ARO7P";
            case "DE":
                return "A1PA6795UKMFR9";
            case "MX":
                return "A1AM78C64UM0Y8";
            default:
                return "ATVPDKIKX0DER";
        }
    }

    /**
     * 确定供应商代码
     */
    private String determineVendorCode(Integer publishType, String site) {
        if (publishType == null) return "WM741";

        // 根据刊登类型和站点确定供应商代码
        if (publishType == 5) { // VCDF
            return "WM741";
        } else if (publishType == 6) { // VCPO
            return "IH75B";
        }

        return "WM741"; // 默认值
    }

    /**
     * 比较新旧ASIN数据，找出新增、修改、删除的ASIN
     *
     * @param newRecord 新的BD记录
     * @param oldRecord 旧的BD记录
     * @return ASIN比较结果
     */
    private AsinComparisonResult compareAsinData(AmBestDealRecord newRecord, AmBestDealRecord oldRecord) {
        AsinComparisonResult result = new AsinComparisonResult();

        // 获取新旧ASIN列表
        List<AmBestDealAsin> newAsins = newRecord.getAsinList() != null ? newRecord.getAsinList() : new ArrayList<>();
        List<AmBestDealAsin> oldAsins = oldRecord.getAsinList() != null ? oldRecord.getAsinList() : new ArrayList<>();

        // 创建ASIN映射表，便于比较
        Map<String, AmBestDealAsin> oldAsinMap = oldAsins.stream()
                .collect(HashMap::new, (map, asin) -> map.put(asin.getPlatformGoodsId(), asin), HashMap::putAll);
        Map<String, AmBestDealAsin> newAsinMap = newAsins.stream()
                .collect(HashMap::new, (map, asin) -> map.put(asin.getPlatformGoodsId(), asin), HashMap::putAll);

        // 找出新增的ASIN
        for (AmBestDealAsin newAsin : newAsins) {
            if (!oldAsinMap.containsKey(newAsin.getPlatformGoodsId())) {
                result.addAddedAsin(newAsin);
            }
        }

        // 找出删除的ASIN
        for (AmBestDealAsin oldAsin : oldAsins) {
            if (!newAsinMap.containsKey(oldAsin.getPlatformGoodsId())) {
                result.addRemovedAsin(oldAsin);
            }
        }

        // 找出修改的ASIN
        for (AmBestDealAsin newAsin : newAsins) {
            AmBestDealAsin oldAsin = oldAsinMap.get(newAsin.getPlatformGoodsId());
            if (oldAsin != null && isAsinDataChanged(newAsin, oldAsin)) {
                result.addModifiedAsin(newAsin, oldAsin);
            }
        }

        return result;
    }

    /**
     * 判断ASIN数据是否发生变化
     *
     * @param newAsin 新ASIN数据
     * @param oldAsin 旧ASIN数据
     * @return 是否发生变化
     */
    private boolean isAsinDataChanged(AmBestDealAsin newAsin, AmBestDealAsin oldAsin) {
        // 比较关键字段
        return !Objects.equals(newAsin.getCommittedUnits(), oldAsin.getCommittedUnits()) ||
               !Objects.equals(newAsin.getDealPrice(), oldAsin.getDealPrice()) ||
               !Objects.equals(newAsin.getPerUnitFunding(), oldAsin.getPerUnitFunding()) ||
               !Objects.equals(newAsin.getStandardPrice(), oldAsin.getStandardPrice()) ||
               !Objects.equals(newAsin.getReferencePrice(), oldAsin.getReferencePrice()) ||
               !Objects.equals(newAsin.getActualDiscount(), oldAsin.getActualDiscount()) ||
               !Objects.equals(newAsin.getExpectedDemand(), oldAsin.getExpectedDemand());
    }

    /**
     * ASIN比较结果内部类
     */
    private static class AsinComparisonResult {
        private final List<AmBestDealAsin> addedAsins = new ArrayList<>();
        private final List<AmBestDealAsin> removedAsins = new ArrayList<>();
        private final List<AsinModification> modifiedAsins = new ArrayList<>();

        public void addAddedAsin(AmBestDealAsin asin) {
            addedAsins.add(asin);
        }

        public void addRemovedAsin(AmBestDealAsin asin) {
            removedAsins.add(asin);
        }

        public void addModifiedAsin(AmBestDealAsin newAsin, AmBestDealAsin oldAsin) {
            modifiedAsins.add(new AsinModification(newAsin, oldAsin));
        }

        public List<AmBestDealAsin> getAddedAsins() {
            return addedAsins;
        }

        public List<AmBestDealAsin> getRemovedAsins() {
            return removedAsins;
        }

        public List<AsinModification> getModifiedAsins() {
            return modifiedAsins;
        }

        public boolean hasChanges() {
            return !addedAsins.isEmpty() || !removedAsins.isEmpty() || !modifiedAsins.isEmpty();
        }
    }

    /**
     * ASIN修改记录内部类
     */
    private static class AsinModification {
        private final AmBestDealAsin newAsin;
        private final AmBestDealAsin oldAsin;

        public AsinModification(AmBestDealAsin newAsin, AmBestDealAsin oldAsin) {
            this.newAsin = newAsin;
            this.oldAsin = oldAsin;
        }

        public AmBestDealAsin getNewAsin() {
            return newAsin;
        }

        public AmBestDealAsin getOldAsin() {
            return oldAsin;
        }
    }

    /**
     * 比较新旧BD活动数据，找出新增、修改、删除的ASIN以及活动信息变更
     *
     * @param newActivityData 新的BD活动数据
     * @param oldActivityData 旧的BD活动数据
     * @return BD活动比较结果
     */
    private BdActivityComparisonResult compareBdActivityData(BdActivityDTO newActivityData, BdActivityDTO oldActivityData) {
        BdActivityComparisonResult result = new BdActivityComparisonResult();

        // 比较活动基本信息是否变更
        boolean activityInfoChanged = isActivityInfoChanged(newActivityData.getActivityInfo(),
                                                           oldActivityData != null ? oldActivityData.getActivityInfo() : null);
        result.setActivityInfoChanged(activityInfoChanged);

        // 获取新旧ASIN列表
        List<BdActivityDTO.BdAsinInfo> newAsins = newActivityData.getAsinList() != null ?
                                                 newActivityData.getAsinList() : new ArrayList<>();
        List<BdActivityDTO.BdAsinInfo> oldAsins = oldActivityData != null && oldActivityData.getAsinList() != null ?
                                                 oldActivityData.getAsinList() : new ArrayList<>();

        // 创建ASIN映射表，便于比较
        Map<String, BdActivityDTO.BdAsinInfo> oldAsinMap = oldAsins.stream()
                .collect(HashMap::new, (map, asin) -> map.put(asin.getPlatformGoodsId(), asin), HashMap::putAll);
        Map<String, BdActivityDTO.BdAsinInfo> newAsinMap = newAsins.stream()
                .collect(HashMap::new, (map, asin) -> map.put(asin.getPlatformGoodsId(), asin), HashMap::putAll);

        // 找出新增的ASIN
        for (BdActivityDTO.BdAsinInfo newAsin : newAsins) {
            if (!oldAsinMap.containsKey(newAsin.getPlatformGoodsId())) {
                newAsin.setOperationFlag("ADD");
                result.addAddedAsin(newAsin);
            }
        }

        // 找出删除的ASIN
        for (BdActivityDTO.BdAsinInfo oldAsin : oldAsins) {
            if (!newAsinMap.containsKey(oldAsin.getPlatformGoodsId())) {
                oldAsin.setOperationFlag("DELETE");
                result.addRemovedAsin(oldAsin);
            }
        }

        // 找出修改的ASIN
        for (BdActivityDTO.BdAsinInfo newAsin : newAsins) {
            BdActivityDTO.BdAsinInfo oldAsin = oldAsinMap.get(newAsin.getPlatformGoodsId());
            if (oldAsin != null && isBdAsinDataChanged(newAsin, oldAsin)) {
                newAsin.setOperationFlag("UPDATE");
                result.addModifiedAsin(newAsin, oldAsin);
            } else if (oldAsin != null) {
                newAsin.setOperationFlag("NONE");
            }
        }

        return result;
    }

    /**
     * 判断BD活动基本信息是否发生变化
     *
     * @param newInfo 新活动信息
     * @param oldInfo 旧活动信息
     * @return 是否发生变化
     */
    private boolean isActivityInfoChanged(BdActivityDTO.BdActivityInfo newInfo, BdActivityDTO.BdActivityInfo oldInfo) {
        if (oldInfo == null) {
            return true; // 新增活动
        }

        return !Objects.equals(newInfo.getPromotionName(), oldInfo.getPromotionName()) ||
               !Objects.equals(newInfo.getStartDateUtc(), oldInfo.getStartDateUtc()) ||
               !Objects.equals(newInfo.getEndDateUtc(), oldInfo.getEndDateUtc()) ||
               !Objects.equals(newInfo.getStatus(), oldInfo.getStatus()) ||
               !Objects.equals(newInfo.getFundingAgreementId(), oldInfo.getFundingAgreementId()) ||
               !Objects.equals(newInfo.getEventType(), oldInfo.getEventType());
    }

    /**
     * 判断BD ASIN数据是否发生变化
     *
     * @param newAsin 新ASIN数据
     * @param oldAsin 旧ASIN数据
     * @return 是否发生变化
     */
    private boolean isBdAsinDataChanged(BdActivityDTO.BdAsinInfo newAsin, BdActivityDTO.BdAsinInfo oldAsin) {
        // 比较关键字段
        return !Objects.equals(newAsin.getCommittedUnits(), oldAsin.getCommittedUnits()) ||
               !Objects.equals(newAsin.getDealPrice(), oldAsin.getDealPrice()) ||
               !Objects.equals(newAsin.getPerUnitFunding(), oldAsin.getPerUnitFunding()) ||
               !Objects.equals(newAsin.getStandardPrice(), oldAsin.getStandardPrice()) ||
               !Objects.equals(newAsin.getReferencePrice(), oldAsin.getReferencePrice()) ||
               !Objects.equals(newAsin.getActualDiscount(), oldAsin.getActualDiscount()) ||
               !Objects.equals(newAsin.getExpectedDemand(), oldAsin.getExpectedDemand()) ||
               !Objects.equals(newAsin.getLowestDiscount(), oldAsin.getLowestDiscount());
    }

    /**
     * BD活动比较结果内部类
     */
    private static class BdActivityComparisonResult {
        private final List<BdActivityDTO.BdAsinInfo> addedAsins = new ArrayList<>();
        private final List<BdActivityDTO.BdAsinInfo> removedAsins = new ArrayList<>();
        private final List<BdAsinModification> modifiedAsins = new ArrayList<>();
        private boolean activityInfoChanged = false;

        public void addAddedAsin(BdActivityDTO.BdAsinInfo asin) {
            addedAsins.add(asin);
        }

        public void addRemovedAsin(BdActivityDTO.BdAsinInfo asin) {
            removedAsins.add(asin);
        }

        public void addModifiedAsin(BdActivityDTO.BdAsinInfo newAsin, BdActivityDTO.BdAsinInfo oldAsin) {
            modifiedAsins.add(new BdAsinModification(newAsin, oldAsin));
        }

        public List<BdActivityDTO.BdAsinInfo> getAddedAsins() {
            return addedAsins;
        }

        public List<BdActivityDTO.BdAsinInfo> getRemovedAsins() {
            return removedAsins;
        }

        public List<BdAsinModification> getModifiedAsins() {
            return modifiedAsins;
        }

        public boolean isActivityInfoChanged() {
            return activityInfoChanged;
        }

        public void setActivityInfoChanged(boolean activityInfoChanged) {
            this.activityInfoChanged = activityInfoChanged;
        }

        public boolean hasChanges() {
            return !addedAsins.isEmpty() || !removedAsins.isEmpty() || !modifiedAsins.isEmpty() || activityInfoChanged;
        }
    }

    /**
     * BD ASIN修改记录内部类
     */
    private static class BdAsinModification {
        private final BdActivityDTO.BdAsinInfo newAsin;
        private final BdActivityDTO.BdAsinInfo oldAsin;

        public BdAsinModification(BdActivityDTO.BdAsinInfo newAsin, BdActivityDTO.BdAsinInfo oldAsin) {
            this.newAsin = newAsin;
            this.oldAsin = oldAsin;
        }

        public BdActivityDTO.BdAsinInfo getNewAsin() {
            return newAsin;
        }

        public BdActivityDTO.BdAsinInfo getOldAsin() {
            return oldAsin;
        }
    }

}
