package com.suncent.smc.provider.biz.promotion;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsin;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import com.suncent.smc.persistence.promotion.service.IAmBestDealAsinService;
import com.suncent.smc.persistence.promotion.service.IAmBestDealRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 亚马逊VC促销活动业务处理类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class AmazonVcPromotionBiz {

    @Autowired
    private IAmBestDealRecordService amBestDealRecordService;

    @Autowired
    private IAmBestDealAsinService amBestDealAsinService;

    /**
     * 根据promotion_id构建亚马逊VC促销活动JSON（简化格式）
     * 新增活动使用
     *
     * @param promotionId 促销ID
     * @return 促销活动JSON字符串
     */
    public String buildAddBDPromotionJson(String promotionId) {
        if (StrUtil.isBlank(promotionId)) {
            throw new BusinessException("促销ID不能为空");
        }

        // 1. 查询促销记录
        AmBestDealRecord promotionRecord = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
        if (promotionRecord == null) {
            throw new BusinessException("未找到促销记录，促销ID: " + promotionId);
        }

        // 2. 查询关联的ASIN列表
        List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(promotionRecord.getId());
        if (CollUtil.isEmpty(asinList)) {
            throw new BusinessException("未找到关联的ASIN记录，促销ID: " + promotionId);
        }

        // 3. 构建简化的促销活动JSON
        Map<String, Object> result = buildSimplifiedPromotionData(promotionRecord, asinList);

        // 4. 转换为JSON字符串
        String jsonResult = JSONUtil.toJsonPrettyStr(result);

        log.info("成功构建促销活动JSON，促销ID: {}, ASIN数量: {}", promotionId, asinList.size());

        return jsonResult;
    }

    /**
     * 根据promotion_id构建亚马逊VC促销活动更新JSON
     * 更新活动使用
     *
     * @param promotionId 促销ID
     * @return 促销活动更新JSON字符串
     */
    public String buildUpdatePromotionJson(String promotionId) {
        if (StrUtil.isBlank(promotionId)) {
            throw new BusinessException("促销ID不能为空");
        }

        // 1. 查询促销记录
        AmBestDealRecord promotionRecord = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
        if (promotionRecord == null) {
            throw new BusinessException("未找到促销记录，促销ID: " + promotionId);
        }

        // 2. 查询关联的ASIN列表
        List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(promotionRecord.getId());
        if (CollUtil.isEmpty(asinList)) {
            throw new BusinessException("未找到关联的ASIN记录，促销ID: " + promotionId);
        }

        // 3. 构建更新促销活动JSON
        Map<String, Object> result = buildUpdatePromotionData(promotionRecord, asinList);

        // 4. 转换为JSON字符串
        String jsonResult = JSONUtil.toJsonPrettyStr(result);

        log.info("成功构建更新促销活动JSON，促销ID: {}, ASIN数量: {}", promotionId, asinList.size());

        return jsonResult;
    }


    /**
     * 构建简化的促销活动数据（新格式）
     *
     * @param record   促销记录
     * @param asinList ASIN列表
     * @return 简化的促销活动数据
     */
    private Map<String, Object> buildSimplifiedPromotionData(AmBestDealRecord record, List<AmBestDealAsin> asinList) {
        Map<String, Object> result = new HashMap<>();

        // 构建产品列表
        List<Map<String, Object>> products = new ArrayList<>();
        for (AmBestDealAsin asin : asinList) {
            Map<String, Object> product = new HashMap<>();
            product.put("asin", asin.getPlatformGoodsId());
            product.put("sku", asin.getPlatformGoodsId());

            // 促销数量
            Map<String, Object> promotionQuantity = new HashMap<>();
            promotionQuantity.put("units", asin.getCommittedUnits() != null ? asin.getCommittedUnits() : 0);
            product.put("promotionQuantity", promotionQuantity);

            // 单位资金
            Map<String, Object> perUnitFunding = new HashMap<>();
            Map<String, Object> fundingValue = new HashMap<>();
            fundingValue.put("amount", asin.getPerUnitFunding() != null ? asin.getPerUnitFunding() : BigDecimal.ZERO);
            fundingValue.put("currencyCode", "USD");
            perUnitFunding.put("value", fundingValue);
            product.put("perUnitFunding", perUnitFunding);

            // 促销价格
            Map<String, Object> promotionPrice = new HashMap<>();
            Map<String, Object> priceValue = new HashMap<>();
            priceValue.put("amount", asin.getDealPrice() != null ? asin.getDealPrice() : BigDecimal.ZERO);
            priceValue.put("currencyCode", "USD");
            promotionPrice.put("value", priceValue);
            product.put("promotionPrice", promotionPrice);

            products.add(product);
        }
        result.put("products", products);

        // 构建促销信息
        Map<String, Object> promotion = new HashMap<>();
        promotion.put("internalDescription", buildInternalDescription(record));

        // 特色ASIN（使用第一个ASIN）
        if (!asinList.isEmpty()) {
            promotion.put("featuredAsin", asinList.get(0).getPlatformGoodsId());
        }

        promotion.put("marketplaceId", getMarketplaceId(record.getSite()));
        promotion.put("offeringName", "BEST_DEAL");

        // 时间安排
        Map<String, Object> schedule = new HashMap<>();
        schedule.put("startDate", convertToSimpleTimeFormat(record.getStartDateUtc()));
        schedule.put("endDate", convertToSimpleTimeFormat(record.getEndDateUtc()));
        promotion.put("schedule", schedule);

        // 所有者信息
        Map<String, Object> owner = new HashMap<>();
        owner.put("vendorCode", determineVendorCode(record.getPublishType(), record.getSite()));
        promotion.put("owner", owner);

        result.put("promotion", promotion);

        return result;
    }

    /**
     * 构建更新促销活动数据（更新格式）
     *
     * @param record   促销记录
     * @param asinList ASIN列表
     * @return 更新促销活动数据
     */
    private Map<String, Object> buildUpdatePromotionData(AmBestDealRecord record, List<AmBestDealAsin> asinList) {
        Map<String, Object> result = new HashMap<>();

        // 设置促销ID
        result.put("promotionId", record.getPromotionId());

        // 构建促销信息
        Map<String, Object> promotion = new HashMap<>();
        promotion.put("internalDescription", buildInternalDescription(record));

        // 特色ASIN（使用第一个ASIN）
        if (!asinList.isEmpty()) {
            promotion.put("featuredAsin", asinList.get(0).getPlatformGoodsId());
        }

        promotion.put("marketplaceId", getMarketplaceId(record.getSite()));
        promotion.put("offeringName", "BEST_DEAL");

        // 时间安排
        Map<String, Object> schedule = new HashMap<>();
        schedule.put("startDate", convertToSimpleTimeFormat(record.getStartDateUtc()));
        schedule.put("endDate", convertToSimpleTimeFormat(record.getEndDateUtc()));
        promotion.put("schedule", schedule);

        // 所有者信息
        Map<String, Object> owner = new HashMap<>();
        owner.put("vendorCode", determineVendorCode(record.getPublishType(), record.getSite()));
        promotion.put("owner", owner);

        result.put("promotion", promotion);

        // 构建更新的产品列表
        List<Map<String, Object>> updatedProducts = new ArrayList<>();
        for (AmBestDealAsin asin : asinList) {
            Map<String, Object> product = new HashMap<>();
            product.put("asin", asin.getPlatformGoodsId());
            product.put("sku", asin.getPlatformGoodsCode() != null ? asin.getPlatformGoodsCode() : asin.getPlatformGoodsId());

            // 促销数量
            Map<String, Object> promotionQuantity = new HashMap<>();
            promotionQuantity.put("units", asin.getCommittedUnits() != null ? asin.getCommittedUnits() : 0);
            product.put("promotionQuantity", promotionQuantity);

            // 单位资金
            Map<String, Object> perUnitFunding = new HashMap<>();
            Map<String, Object> fundingValue = new HashMap<>();
            fundingValue.put("amount", asin.getPerUnitFunding() != null ? asin.getPerUnitFunding() : BigDecimal.ZERO);
            fundingValue.put("currencyCode", "USD");
            perUnitFunding.put("value", fundingValue);
            product.put("perUnitFunding", perUnitFunding);

            // 促销价格
            Map<String, Object> promotionPrice = new HashMap<>();
            Map<String, Object> priceValue = new HashMap<>();
            priceValue.put("amount", asin.getDealPrice() != null ? asin.getDealPrice() : BigDecimal.ZERO);
            priceValue.put("currencyCode", "USD");
            promotionPrice.put("value", priceValue);
            product.put("promotionPrice", promotionPrice);

            updatedProducts.add(product);
        }

        result.put("updatedProducts", updatedProducts);

        // 移除的产品列表（暂时为空）
        result.put("removedProducts", new ArrayList<>());

        return result;
    }

    /**
     * 转换为简单时间格式 (ISO本地时间格式: yyyy-MM-ddTHH:mm:ss)
     */
    private String convertToSimpleTimeFormat(String utcTime) {
        if (StrUtil.isBlank(utcTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        try {
            // 处理带时区信息的时间格式，如: "2025-08-19T23:45:00-07:00[US/Pacific]"
            if (utcTime.contains("[") || utcTime.contains("+") || utcTime.contains("-07:00") || utcTime.contains("-06:00")) {
                // 提取时间部分，去掉时区信息
                String timePart = utcTime;
                if (timePart.contains("[")) {
                    timePart = timePart.substring(0, timePart.indexOf("["));
                }
                if (timePart.contains("+")) {
                    timePart = timePart.substring(0, timePart.indexOf("+"));
                }
                if (timePart.contains("-") && timePart.lastIndexOf("-") > 10) {
                    timePart = timePart.substring(0, timePart.lastIndexOf("-"));
                }
                return timePart;
            }

            // 如果已经是ISO格式，去掉Z后缀
            if (utcTime.contains("T")) {
                return utcTime.replace("Z", "");
            }

            // 如果是其他格式，直接返回
            return utcTime;

        } catch (Exception e) {
            log.error("时间格式转换失败: {}", utcTime, e);
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
    }


    /**
     * 构建内部描述
     */
    private String buildInternalDescription(AmBestDealRecord record) {
        String dateStr = "";
        if (StrUtil.isNotBlank(record.getStartDateUtc())) {
            try {
                // 解析日期并格式化为 "Aug 2, 2025" 格式
                String startDate = record.getStartDateUtc();
                // 如果包含时间部分，只取日期部分
                if (startDate.contains("T")) {
                    startDate = startDate.substring(0, startDate.indexOf("T"));
                }

                // 解析日期 (格式: yyyy-MM-dd)
                String[] dateParts = startDate.split("-");
                if (dateParts.length == 3) {
                    int year = Integer.parseInt(dateParts[0]);
                    int month = Integer.parseInt(dateParts[1]);
                    int day = Integer.parseInt(dateParts[2]);

                    // 月份名称数组
                    String[] monthNames = {"", "Jan", "Feb", "Mar", "Apr", "May", "Jun",
                            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};

                    dateStr = monthNames[month] + " " + day + ", " + year;
                } else {
                    dateStr = "Date";
                }
            } catch (Exception e) {
                dateStr = "Date";
            }
        }

        String vendorCode = determineVendorCode(record.getPublishType(), record.getSite());
        return dateStr + " " + vendorCode + " Best Deal";
    }

    /**
     * 获取市场ID
     */
    private String getMarketplaceId(String site) {
        if (site == null) return "ATVPDKIKX0DER";

        switch (site.toUpperCase()) {
            case "US":
                return "ATVPDKIKX0DER";
            case "UK":
                return "A1F83G8C2ARO7P";
            case "DE":
                return "A1PA6795UKMFR9";
            case "MX":
                return "A1AM78C64UM0Y8";
            default:
                return "ATVPDKIKX0DER";
        }
    }

    /**
     * 确定供应商代码
     */
    private String determineVendorCode(Integer publishType, String site) {
        if (publishType == null) return "WM741";

        // 根据刊登类型和站点确定供应商代码
        if (publishType == 5) { // VCDF
            return "WM741";
        } else if (publishType == 6) { // VCPO
            return "IH75B";
        }

        return "WM741"; // 默认值
    }


}
